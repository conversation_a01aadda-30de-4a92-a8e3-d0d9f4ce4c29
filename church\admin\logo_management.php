<?php
session_start();

require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Check if GD extension is loaded
if (!extension_loaded('gd')) {
    $error_message = "GD extension is not enabled. To use logo management features, please enable the GD extension in your PHP configuration:<br><br>
    <strong>For XAMPP users:</strong><br>
    1. Open your php.ini file (usually in C:\\xampp\\php\\php.ini)<br>
    2. Find the line ';extension=gd' and remove the semicolon to make it 'extension=gd'<br>
    3. Save the file and restart Apache<br><br>
    <strong>For other PHP installations:</strong><br>
    1. Locate your php.ini file<br>
    2. Uncomment or add 'extension=gd'<br>
    3. Restart your web server";
}

// Handle logo upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'upload_logo') {
        $logoType = $_POST['logo_type'] ?? 'main';
        $uploadResult = handleLogoUpload($logoType);
        
        if ($uploadResult['success']) {
            $success_message = $uploadResult['message'];
        } else {
            $error_message = $uploadResult['message'];
        }
    } elseif ($action === 'delete_logo') {
        $logoType = $_POST['logo_type'] ?? 'main';
        $deleteResult = deleteLogo($logoType);
        
        if ($deleteResult['success']) {
            $success_message = $deleteResult['message'];
        } else {
            $error_message = $deleteResult['message'];
        }
    } elseif ($action === 'generate_favicon') {
        $faviconResult = generateFavicon();
        
        if ($faviconResult['success']) {
            $success_message = $faviconResult['message'];
        } else {
            $error_message = $faviconResult['message'];
        }
    }
}

function handleLogoUpload($logoType) {
    // Check if GD extension is available
    if (!extension_loaded('gd')) {
        return ['success' => false, 'message' => 'GD extension is not enabled. Please enable GD extension in PHP to upload and process logos.'];
    }

    if (!isset($_FILES['logo_file']) || $_FILES['logo_file']['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'No file uploaded or upload error occurred.'];
    }
    
    $file = $_FILES['logo_file'];
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type. Please upload JPG, PNG, GIF, WebP, or SVG files only.'];
    }
    
    // Check file size (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        return ['success' => false, 'message' => 'File size too large. Maximum size is 5MB.'];
    }
    
    // Create upload directory if it doesn't exist
    $uploadDir = __DIR__ . '/../assets/img/logos/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Generate filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = $logoType . '_logo.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        // Process image if not SVG
        if ($file['type'] !== 'image/svg+xml') {
            $processResult = processLogoImage($filepath, $logoType);
            if (!$processResult['success']) {
                return $processResult;
            }
        }
        
        // Save logo path to database
        $relativePath = 'assets/img/logos/' . $filename;
        update_site_setting($logoType . '_logo', $relativePath);
        
        // Generate additional sizes if main logo
        if ($logoType === 'main') {
            generateLogoVariants($filepath);
        }
        
        return ['success' => true, 'message' => ucfirst($logoType) . ' logo uploaded successfully!'];
    } else {
        return ['success' => false, 'message' => 'Failed to save uploaded file.'];
    }
}

function processLogoImage($filepath, $logoType) {
    // Check if GD extension is available
    if (!extension_loaded('gd')) {
        return ['success' => false, 'message' => 'GD extension is not enabled. Please enable GD extension in PHP to process images.'];
    }

    $imageInfo = getimagesize($filepath);
    if (!$imageInfo) {
        return ['success' => false, 'message' => 'Invalid image file.'];
    }
    
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    $type = $imageInfo[2];
    
    // Create image resource
    switch ($type) {
        case IMAGETYPE_JPEG:
            $image = imagecreatefromjpeg($filepath);
            break;
        case IMAGETYPE_PNG:
            $image = imagecreatefrompng($filepath);
            break;
        case IMAGETYPE_GIF:
            $image = imagecreatefromgif($filepath);
            break;
        case IMAGETYPE_WEBP:
            $image = imagecreatefromwebp($filepath);
            break;
        default:
            return ['success' => false, 'message' => 'Unsupported image type.'];
    }
    
    if (!$image) {
        return ['success' => false, 'message' => 'Failed to process image.'];
    }
    
    // Optimize image based on logo type
    $optimized = optimizeLogoImage($image, $width, $height, $logoType);
    
    // Save optimized image
    $success = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $success = imagejpeg($optimized, $filepath, 90);
            break;
        case IMAGETYPE_PNG:
            imagealphablending($optimized, false);
            imagesavealpha($optimized, true);
            $success = imagepng($optimized, $filepath, 6);
            break;
        case IMAGETYPE_GIF:
            $success = imagegif($optimized, $filepath);
            break;
        case IMAGETYPE_WEBP:
            $success = imagewebp($optimized, $filepath, 90);
            break;
    }
    
    imagedestroy($image);
    imagedestroy($optimized);
    
    return ['success' => $success, 'message' => $success ? 'Image processed successfully.' : 'Failed to save processed image.'];
}

function optimizeLogoImage($image, $width, $height, $logoType) {
    // Define target dimensions based on logo type
    $targetDimensions = [
        'main' => ['width' => 300, 'height' => 100],
        'header' => ['width' => 200, 'height' => 60],
        'email' => ['width' => 250, 'height' => 80],
        'favicon' => ['width' => 64, 'height' => 64]
    ];
    
    $target = $targetDimensions[$logoType] ?? $targetDimensions['main'];
    
    // Calculate new dimensions maintaining aspect ratio
    $aspectRatio = $width / $height;
    $targetAspectRatio = $target['width'] / $target['height'];
    
    if ($aspectRatio > $targetAspectRatio) {
        // Image is wider than target
        $newWidth = $target['width'];
        $newHeight = $target['width'] / $aspectRatio;
    } else {
        // Image is taller than target
        $newHeight = $target['height'];
        $newWidth = $target['height'] * $aspectRatio;
    }

    // Cast to integers to avoid precision warnings
    $newWidth = (int)round($newWidth);
    $newHeight = (int)round($newHeight);

    // Create new image
    $optimized = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preserve transparency for PNG and GIF
    imagealphablending($optimized, false);
    imagesavealpha($optimized, true);
    $transparent = imagecolorallocatealpha($optimized, 255, 255, 255, 127);
    imagefill($optimized, 0, 0, $transparent);
    
    // Resize image
    imagecopyresampled($optimized, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
    
    return $optimized;
}

function generateLogoVariants($mainLogoPath) {
    $imageInfo = getimagesize($mainLogoPath);
    if (!$imageInfo) return;
    
    $type = $imageInfo[2];
    
    // Create image resource
    switch ($type) {
        case IMAGETYPE_JPEG:
            $image = imagecreatefromjpeg($mainLogoPath);
            break;
        case IMAGETYPE_PNG:
            $image = imagecreatefrompng($mainLogoPath);
            break;
        case IMAGETYPE_GIF:
            $image = imagecreatefromgif($mainLogoPath);
            break;
        case IMAGETYPE_WEBP:
            $image = imagecreatefromwebp($mainLogoPath);
            break;
        default:
            return;
    }
    
    if (!$image) return;
    
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    
    // Generate header logo variant
    $headerLogo = optimizeLogoImage($image, $width, $height, 'header');
    $headerPath = str_replace('main_logo.', 'header_logo.', $mainLogoPath);
    
    switch ($type) {
        case IMAGETYPE_JPEG:
            imagejpeg($headerLogo, $headerPath, 90);
            break;
        case IMAGETYPE_PNG:
            imagealphablending($headerLogo, false);
            imagesavealpha($headerLogo, true);
            imagepng($headerLogo, $headerPath, 6);
            break;
        case IMAGETYPE_GIF:
            imagegif($headerLogo, $headerPath);
            break;
        case IMAGETYPE_WEBP:
            imagewebp($headerLogo, $headerPath, 90);
            break;
    }
    
    // Generate email logo variant
    $emailLogo = optimizeLogoImage($image, $width, $height, 'email');
    $emailPath = str_replace('main_logo.', 'email_logo.', $mainLogoPath);
    
    switch ($type) {
        case IMAGETYPE_JPEG:
            imagejpeg($emailLogo, $emailPath, 90);
            break;
        case IMAGETYPE_PNG:
            imagealphablending($emailLogo, false);
            imagesavealpha($emailLogo, true);
            imagepng($emailLogo, $emailPath, 6);
            break;
        case IMAGETYPE_GIF:
            imagegif($emailLogo, $emailPath);
            break;
        case IMAGETYPE_WEBP:
            imagewebp($emailLogo, $emailPath, 90);
            break;
    }
    
    // Save variant paths to database
    $extension = pathinfo($mainLogoPath, PATHINFO_EXTENSION);
    update_site_setting('header_logo', 'assets/img/logos/header_logo.' . $extension);
    update_site_setting('email_logo', 'assets/img/logos/email_logo.' . $extension);
    
    imagedestroy($image);
    imagedestroy($headerLogo);
    imagedestroy($emailLogo);
}

function generateFavicon() {
    $mainLogoPath = get_site_setting('main_logo', '');
    if (!$mainLogoPath) {
        return ['success' => false, 'message' => 'No main logo found. Please upload a main logo first.'];
    }
    
    $fullPath = __DIR__ . '/../' . $mainLogoPath;
    if (!file_exists($fullPath)) {
        return ['success' => false, 'message' => 'Main logo file not found.'];
    }
    
    $imageInfo = getimagesize($fullPath);
    if (!$imageInfo) {
        return ['success' => false, 'message' => 'Invalid main logo file.'];
    }
    
    $type = $imageInfo[2];
    
    // Create image resource
    switch ($type) {
        case IMAGETYPE_JPEG:
            $image = imagecreatefromjpeg($fullPath);
            break;
        case IMAGETYPE_PNG:
            $image = imagecreatefrompng($fullPath);
            break;
        case IMAGETYPE_GIF:
            $image = imagecreatefromgif($fullPath);
            break;
        case IMAGETYPE_WEBP:
            $image = imagecreatefromwebp($fullPath);
            break;
        default:
            return ['success' => false, 'message' => 'Unsupported image type for favicon generation.'];
    }
    
    if (!$image) {
        return ['success' => false, 'message' => 'Failed to process main logo for favicon generation.'];
    }
    
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    
    // Generate different favicon sizes
    $faviconSizes = [16, 32, 48, 64, 128, 256];
    $faviconDir = __DIR__ . '/../assets/img/favicons/';
    
    if (!is_dir($faviconDir)) {
        mkdir($faviconDir, 0755, true);
    }
    
    foreach ($faviconSizes as $size) {
        $favicon = imagecreatetruecolor($size, $size);
        
        // Preserve transparency
        imagealphablending($favicon, false);
        imagesavealpha($favicon, true);
        $transparent = imagecolorallocatealpha($favicon, 255, 255, 255, 127);
        imagefill($favicon, 0, 0, $transparent);
        
        // Resize to square favicon
        imagecopyresampled($favicon, $image, 0, 0, 0, 0, $size, $size, $width, $height);
        
        // Save as PNG
        $faviconPath = $faviconDir . "favicon-{$size}x{$size}.png";
        imagepng($favicon, $faviconPath, 6);
        imagedestroy($favicon);
    }
    
    // Generate ICO file (16x16 and 32x32)
    $icoPath = __DIR__ . '/../favicon.ico';
    generateIcoFile($image, $width, $height, $icoPath);
    
    // Update database settings
    update_site_setting('favicon_16', 'assets/img/favicons/favicon-16x16.png');
    update_site_setting('favicon_32', 'assets/img/favicons/favicon-32x32.png');
    update_site_setting('favicon_ico', 'favicon.ico');
    
    imagedestroy($image);
    
    return ['success' => true, 'message' => 'Favicon generated successfully in multiple sizes!'];
}

function generateIcoFile($image, $width, $height, $outputPath) {
    // Create 16x16 and 32x32 versions
    $sizes = [16, 32];
    $images = [];
    
    foreach ($sizes as $size) {
        $resized = imagecreatetruecolor($size, $size);
        imagealphablending($resized, false);
        imagesavealpha($resized, true);
        $transparent = imagecolorallocatealpha($resized, 255, 255, 255, 127);
        imagefill($resized, 0, 0, $transparent);
        
        imagecopyresampled($resized, $image, 0, 0, 0, 0, $size, $size, $width, $height);
        $images[] = $resized;
    }
    
    // Simple ICO generation (basic implementation)
    $ico = '';
    $ico .= pack('vvv', 0, 1, count($images)); // ICO header
    
    $offset = 6 + (count($images) * 16);
    
    foreach ($images as $i => $img) {
        $size = $sizes[$i];
        
        // Get PNG data
        ob_start();
        imagepng($img);
        $pngData = ob_get_clean();
        
        // ICO directory entry
        $ico .= pack('CCCCvvVV', $size, $size, 0, 0, 1, 32, strlen($pngData), $offset);
        $offset += strlen($pngData);
    }
    
    // Add PNG data
    foreach ($images as $img) {
        ob_start();
        imagepng($img);
        $ico .= ob_get_clean();
        imagedestroy($img);
    }
    
    file_put_contents($outputPath, $ico);
}

function deleteLogo($logoType) {
    $logoPath = get_site_setting($logoType . '_logo', '');
    if (!$logoPath) {
        return ['success' => false, 'message' => 'No logo found to delete.'];
    }
    
    $fullPath = __DIR__ . '/../' . $logoPath;
    if (file_exists($fullPath)) {
        if (unlink($fullPath)) {
            update_site_setting($logoType . '_logo', '');
            return ['success' => true, 'message' => ucfirst($logoType) . ' logo deleted successfully!'];
        } else {
            return ['success' => false, 'message' => 'Failed to delete logo file.'];
        }
    } else {
        update_site_setting($logoType . '_logo', '');
        return ['success' => true, 'message' => 'Logo reference removed (file was already missing).'];
    }
}

// Get current logo settings
$logoTypes = ['main', 'header', 'email', 'favicon'];
$currentLogos = [];
foreach ($logoTypes as $type) {
    $currentLogos[$type] = get_site_setting($type . '_logo', '');
}

$page_title = "Logo Management";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="bi bi-image"></i> Logo Management
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <?php if (extension_loaded('gd')): ?>
                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="bi bi-upload"></i> Upload Logo
                        </button>
                    <?php else: ?>
                        <button type="button" class="btn btn-outline-secondary" disabled title="GD extension required">
                            <i class="bi bi-upload"></i> Upload Logo (GD Required)
                        </button>
                    <?php endif; ?>
                </div>
            </div>

            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Current Logos Display -->
            <div class="row">
                <?php
                $logoConfigs = [
                    'main' => [
                        'title' => 'Main Logo',
                        'description' => 'Primary organization logo used throughout the system',
                        'icon' => 'bi-star-fill',
                        'recommended' => '300x100px'
                    ],
                    'header' => [
                        'title' => 'Header Logo',
                        'description' => 'Smaller logo for navigation headers',
                        'icon' => 'bi-layout-navbar',
                        'recommended' => '200x60px'
                    ],
                    'email' => [
                        'title' => 'Email Logo',
                        'description' => 'Logo used in email templates',
                        'icon' => 'bi-envelope',
                        'recommended' => '250x80px'
                    ],
                    'favicon' => [
                        'title' => 'Favicon',
                        'description' => 'Small icon displayed in browser tabs',
                        'icon' => 'bi-browser-chrome',
                        'recommended' => '64x64px'
                    ]
                ];

                foreach ($logoConfigs as $type => $config):
                    $logoPath = $currentLogos[$type];
                    $hasLogo = !empty($logoPath) && file_exists(__DIR__ . '/../' . $logoPath);
                ?>
                <div class="col-lg-6 col-xl-3 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex align-items-center">
                            <i class="<?php echo $config['icon']; ?> me-2"></i>
                            <h6 class="mb-0"><?php echo $config['title']; ?></h6>
                        </div>
                        <div class="card-body text-center">
                            <div class="logo-preview mb-3" style="height: 120px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 1px dashed #dee2e6; border-radius: 0.375rem;">
                                <?php if ($hasLogo): ?>
                                    <img src="<?php echo htmlspecialchars('../' . $logoPath); ?>" alt="<?php echo $config['title']; ?>" style="max-width: 100%; max-height: 100%; object-fit: contain;">
                                <?php else: ?>
                                    <div class="text-muted">
                                        <i class="<?php echo $config['icon']; ?> fs-1 mb-2"></i>
                                        <div>No logo uploaded</div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <p class="small text-muted mb-2"><?php echo $config['description']; ?></p>
                            <p class="small text-info mb-3">Recommended: <?php echo $config['recommended']; ?></p>

                            <div class="btn-group-vertical w-100">
                                <?php if (extension_loaded('gd')): ?>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="uploadLogo('<?php echo $type; ?>')">
                                        <i class="bi bi-upload"></i> Upload
                                    </button>
                                <?php else: ?>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" disabled title="GD extension required">
                                        <i class="bi bi-upload"></i> Upload
                                    </button>
                                <?php endif; ?>
                                <?php if ($hasLogo): ?>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteLogo('<?php echo $type; ?>')">
                                        <i class="bi bi-trash"></i> Delete
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Logo Tools -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-tools"></i> Logo Tools
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Favicon Generator</h6>
                            <p class="text-muted">Generate favicon files from your main logo in multiple sizes (16x16, 32x32, 48x48, etc.)</p>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="action" value="generate_favicon">
                                <button type="submit" class="btn btn-outline-secondary" <?php echo empty($currentLogos['main']) ? 'disabled' : ''; ?>>
                                    <i class="bi bi-gear"></i> Generate Favicon
                                </button>
                            </form>
                            <?php if (empty($currentLogos['main'])): ?>
                                <small class="text-muted d-block mt-1">Upload a main logo first</small>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6">
                            <h6>Logo Guidelines</h6>
                            <ul class="small text-muted">
                                <li>Use PNG format for logos with transparency</li>
                                <li>SVG format is recommended for scalability</li>
                                <li>Maximum file size: 5MB</li>
                                <li>Logos are automatically optimized and resized</li>
                                <li>Main logo generates header and email variants automatically</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logo Usage Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i> Logo Usage
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Where Logos Are Used</h6>
                            <ul class="small">
                                <li><strong>Main Logo:</strong> Site header, login page, admin dashboard</li>
                                <li><strong>Header Logo:</strong> Navigation bar, mobile menu</li>
                                <li><strong>Email Logo:</strong> Email templates, newsletters</li>
                                <li><strong>Favicon:</strong> Browser tabs, bookmarks, mobile shortcuts</li>
                            </ul>
                        </div>

                        <div class="col-md-6">
                            <h6>Technical Details</h6>
                            <ul class="small">
                                <li>Logos are stored in <code>assets/img/logos/</code></li>
                                <li>Favicons are stored in <code>assets/img/favicons/</code></li>
                                <li>Logo paths are saved in site settings</li>
                                <li>Images are automatically optimized on upload</li>
                                <li>Multiple formats supported: JPG, PNG, GIF, WebP, SVG</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Logo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data" id="uploadForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="upload_logo">
                    <input type="hidden" name="logo_type" id="logoType" value="main">

                    <div class="mb-3">
                        <label for="logoTypeSelect" class="form-label">Logo Type</label>
                        <select class="form-select" id="logoTypeSelect" name="logo_type">
                            <option value="main">Main Logo</option>
                            <option value="header">Header Logo</option>
                            <option value="email">Email Logo</option>
                            <option value="favicon">Favicon</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="logoFile" class="form-label">Choose Logo File</label>
                        <input type="file" class="form-control" id="logoFile" name="logo_file" accept="image/*" required>
                        <div class="form-text">
                            Supported formats: JPG, PNG, GIF, WebP, SVG. Maximum size: 5MB.
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> Upload Tips</h6>
                            <ul class="mb-0 small">
                                <li>Images will be automatically resized and optimized</li>
                                <li>Uploading a main logo will generate header and email variants</li>
                                <li>Use high-resolution images for best quality</li>
                                <li>PNG format recommended for logos with transparency</li>
                            </ul>
                        </div>
                    </div>

                    <div id="imagePreview" class="text-center" style="display: none;">
                        <img id="previewImage" src="" alt="Preview" style="max-width: 100%; max-height: 200px; border: 1px solid #dee2e6; border-radius: 0.375rem;">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-upload"></i> Upload Logo
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function uploadLogo(type) {
    document.getElementById('logoType').value = type;
    document.getElementById('logoTypeSelect').value = type;

    // Update modal title
    const titles = {
        'main': 'Main Logo',
        'header': 'Header Logo',
        'email': 'Email Logo',
        'favicon': 'Favicon'
    };
    document.querySelector('#uploadModal .modal-title').textContent = 'Upload ' + titles[type];

    // Show modal
    new bootstrap.Modal(document.getElementById('uploadModal')).show();
}

function deleteLogo(type) {
    const titles = {
        'main': 'Main Logo',
        'header': 'Header Logo',
        'email': 'Email Logo',
        'favicon': 'Favicon'
    };

    if (confirm('Are you sure you want to delete the ' + titles[type] + '? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_logo">
            <input type="hidden" name="logo_type" value="${type}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Image preview functionality
document.getElementById('logoFile').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImage').src = e.target.result;
            document.getElementById('imagePreview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        document.getElementById('imagePreview').style.display = 'none';
    }
});
</script>

<?php include 'includes/footer.php'; ?>
