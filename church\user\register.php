<?php
/**
 * User Registration Page
 * 
 * Allows new users to register with password input
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Redirect if already logged in
if ($userAuth->isAuthenticated()) {
    header("Location: dashboard.php");
    exit();
}

// Process registration form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['register'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        // Sanitize and validate input
        $fullName = $security->sanitizeInput($_POST['full_name'], 'text');
        $firstName = $security->sanitizeInput($_POST['first_name'], 'text');
        $lastName = $security->sanitizeInput($_POST['last_name'], 'text');
        $email = $security->sanitizeInput($_POST['email'], 'email');
        $phoneNumber = $security->sanitizeInput($_POST['phone_number'], 'text');
        $password = $_POST['password'];
        $confirmPassword = $_POST['confirm_password'];
        $birthDate = $_POST['birth_date'];
        $occupation = $security->sanitizeInput($_POST['occupation'], 'text');
        $homeAddress = $security->sanitizeInput($_POST['home_address'], 'text');
        
        // Validation
        if (empty($fullName) || empty($email) || empty($password)) {
            $error = "Please fill in all required fields.";
        } elseif (!$security->validateInput($email, 'email')) {
            $error = "Please enter a valid email address.";
        } elseif ($password !== $confirmPassword) {
            $error = "Passwords do not match.";
        } elseif (!$security->validateInput($password, 'password')) {
            $error = "Password does not meet security requirements. Please use a stronger password.";
        } else {
            // Check if email already exists
            $stmt = $pdo->prepare("SELECT id FROM members WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                $error = "An account with this email address already exists.";
            } else {
                try {
                    // Hash password
                    $hashedPassword = $security->hashPassword($password);
                    
                    // Prepare user data
                    $userData = [
                        'full_name' => $fullName,
                        'first_name' => $firstName,
                        'last_name' => $lastName,
                        'email' => $email,
                        'phone_number' => $phoneNumber,
                        'birth_date' => $birthDate,
                        'occupation' => $occupation,
                        'home_address' => $homeAddress,
                        'password_hash' => $hashedPassword,
                        'must_change_password' => 0,
                        'status' => 'active'
                    ];

                    // Insert user
                    $stmt = $pdo->prepare("
                        INSERT INTO members
                        (full_name, first_name, last_name, email, phone_number, birth_date,
                         occupation, home_address, password_hash, must_change_password, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");

                    $stmt->execute([
                        $userData['full_name'],
                        $userData['first_name'],
                        $userData['last_name'],
                        $userData['email'],
                        $userData['phone_number'],
                        $userData['birth_date'],
                        $userData['occupation'],
                        $userData['home_address'],
                        $userData['password_hash'],
                        $userData['must_change_password'],
                        $userData['status']
                    ]);
                    
                    $userId = $pdo->lastInsertId();
                    
                    // Log activity
                    $userAuth->logUserActivity($userId, 'registration', 'User registered new account');
                    
                    $success = "Registration successful! You can now log in with your credentials.";
                    
                    // Optionally send welcome email here
                    
                } catch (PDOException $e) {
                    if ($e->getCode() == 23000) { // Duplicate entry
                        $error = "An account with this email address already exists.";
                    } else {
                        error_log("Registration error: " . $e->getMessage());
                        $error = "An error occurred during registration. Please try again.";
                    }
                }
            }
        }
    }
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Registration - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .register-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 2.5rem;
            margin: 0 auto;
            max-width: 800px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .register-logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .register-logo h1 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .register-logo p {
            color: #7f8c8d;
            font-size: 1rem;
            margin: 0;
        }
        
        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background-color: #fee;
            color: #c33;
        }
        
        .alert-success {
            background-color: #efe;
            color: #363;
        }
        
        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .required-field::after {
            content: " *";
            color: #dc3545;
        }
        
        .password-requirements {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
        
        .register-links {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .register-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .register-links a:hover {
            color: #764ba2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="register-container">
            <div class="register-logo">
                <h1><i class="bi bi-person-plus-fill"></i> Join Our Community</h1>
                <p><?php echo htmlspecialchars($sitename); ?></p>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                    <div class="mt-2">
                        <a href="login.php" class="btn btn-success btn-sm">
                            <i class="bi bi-box-arrow-in-right"></i> Go to Login
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                    <?php echo $security->generateCSRFInput(); ?>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="full_name" class="form-label required-field">Full Name</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   placeholder="Enter your full name" required 
                                   value="<?php echo isset($_POST['full_name']) ? htmlspecialchars($_POST['full_name']) : ''; ?>">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   placeholder="First name"
                                   value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   placeholder="Last name"
                                   value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label required-field">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   placeholder="<EMAIL>" required
                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone_number" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                                   placeholder="+1234567890"
                                   value="<?php echo isset($_POST['phone_number']) ? htmlspecialchars($_POST['phone_number']) : ''; ?>">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label required-field">Password</label>
                            <input type="password" class="form-control" id="password" name="password" 
                                   placeholder="Create a strong password" required>
                            <div class="password-requirements">
                                Password must be at least 8 characters long and contain uppercase, lowercase, numbers, and special characters.
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label required-field">Confirm Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                   placeholder="Confirm your password" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="birth_date" class="form-label">Date of Birth</label>
                            <input type="date" class="form-control" id="birth_date" name="birth_date"
                                   value="<?php echo isset($_POST['birth_date']) ? htmlspecialchars($_POST['birth_date']) : ''; ?>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="occupation" class="form-label">Occupation</label>
                            <input type="text" class="form-control" id="occupation" name="occupation" 
                                   placeholder="Your occupation"
                                   value="<?php echo isset($_POST['occupation']) ? htmlspecialchars($_POST['occupation']) : ''; ?>">
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="home_address" class="form-label">Home Address</label>
                        <textarea class="form-control" id="home_address" name="home_address" rows="3" 
                                  placeholder="Your home address"><?php echo isset($_POST['home_address']) ? htmlspecialchars($_POST['home_address']) : ''; ?></textarea>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="login.php" class="btn btn-secondary me-md-2">
                            <i class="bi bi-arrow-left"></i> Back to Login
                        </a>
                        <button type="submit" name="register" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> Create Account
                        </button>
                    </div>
                </form>
            <?php endif; ?>
            
            <div class="register-links">
                <div>
                    <a href="../index.php">
                        <i class="bi bi-house"></i> Back to homepage
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // Auto-populate full name from first and last name
        function updateFullName() {
            const firstName = document.getElementById('first_name').value.trim();
            const lastName = document.getElementById('last_name').value.trim();
            const fullNameField = document.getElementById('full_name');
            
            if (firstName || lastName) {
                fullNameField.value = (firstName + ' ' + lastName).trim();
            }
        }
        
        document.getElementById('first_name').addEventListener('input', updateFullName);
        document.getElementById('last_name').addEventListener('input', updateFullName);
    </script>
</body>
</html>
