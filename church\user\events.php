<?php
/**
 * User Events Interface
 * 
 * Allows authenticated users to view and manage their event participation
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Check if user must change password (redirect to change password page)
if ($userData['must_change_password']) {
    header("Location: change_password.php");
    exit();
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}

// Get upcoming events (placeholder - will be implemented in Event Management Interface task)
$upcomingEvents = [];
$myEvents = [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Events - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: white !important;
        }
        
        .events-container {
            margin-top: 2rem;
        }
        
        .events-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
        }
        
        .events-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .coming-soon {
            text-align: center;
            padding: 3rem 2rem;
            color: #6c757d;
        }
        
        .coming-soon i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #667eea;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list i {
            color: #667eea;
            margin-right: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="events.php">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php"><i class="bi bi-shield-lock"></i> Change Password</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="bi bi-gear"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container events-container">
        <!-- Events Header -->
        <div class="events-header">
            <h1><i class="bi bi-calendar-event"></i> Events & Activities</h1>
            <p class="mb-0">Stay connected with upcoming events and activities</p>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="events-card">
                    <div class="coming-soon">
                        <i class="bi bi-calendar-plus"></i>
                        <h3>Event Management Coming Soon!</h3>
                        <p class="lead">We're working hard to bring you a comprehensive event management system.</p>
                        <p>This feature will allow you to:</p>
                        
                        <ul class="feature-list text-start d-inline-block">
                            <li><i class="bi bi-check-circle"></i> View upcoming church events and activities</li>
                            <li><i class="bi bi-check-circle"></i> RSVP to events you want to attend</li>
                            <li><i class="bi bi-check-circle"></i> Manage your event calendar</li>
                            <li><i class="bi bi-check-circle"></i> Receive event reminders and notifications</li>
                            <li><i class="bi bi-check-circle"></i> View event details, locations, and requirements</li>
                            <li><i class="bi bi-check-circle"></i> Track your event participation history</li>
                        </ul>
                        
                        <div class="mt-4">
                            <a href="dashboard.php" class="btn btn-primary">
                                <i class="bi bi-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Events Sidebar -->
            <div class="col-lg-4">
                <div class="events-card">
                    <h5 class="mb-3"><i class="bi bi-calendar-check"></i> Quick Stats</h5>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="mb-3">
                                <div class="h4 text-primary">0</div>
                                <small class="text-muted">Upcoming Events</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <div class="h4 text-success">0</div>
                                <small class="text-muted">My RSVPs</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="mb-3">
                                <div class="h4 text-info">0</div>
                                <small class="text-muted">Events Attended</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="mb-3">
                                <div class="h4 text-warning">0</div>
                                <small class="text-muted">Pending Invites</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="events-card">
                    <h5 class="mb-3"><i class="bi bi-bell"></i> Event Notifications</h5>
                    <p class="small text-muted">Stay updated with event notifications. You can manage your notification preferences in your settings.</p>
                    <div class="d-grid">
                        <a href="settings.php" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-gear"></i> Notification Settings
                        </a>
                    </div>
                </div>
                
                <div class="events-card">
                    <h5 class="mb-3"><i class="bi bi-question-circle"></i> Need Help?</h5>
                    <p class="small text-muted">Have questions about events or need assistance with RSVPs? We're here to help!</p>
                    <div class="d-grid">
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-envelope"></i> Contact Events Team
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
