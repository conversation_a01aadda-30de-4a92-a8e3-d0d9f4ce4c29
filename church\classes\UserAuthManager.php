<?php

/**
 * UserAuthManager Class
 * 
 * Handles user authentication functionality for the church management system
 * including login, registration, password management, and session handling.
 */
class UserAuthManager {
    private $pdo;
    private $security;
    private $sessionTimeout = 3600; // 1 hour default
    private $maxLoginAttempts = 5;
    private $lockoutDuration = 1800; // 30 minutes
    
    /**
     * Constructor
     * 
     * @param PDO $pdo Database connection
     * @param SecurityManager $security Security manager instance
     */
    public function __construct($pdo, $security = null) {
        $this->pdo = $pdo;
        $this->security = $security ?: new SecurityManager($pdo);
        
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * Authenticate user with email or phone number
     * 
     * @param string $identifier Email or phone number
     * @param string $password Password
     * @return array Authentication result
     */
    public function authenticateUser($identifier, $password) {
        $result = [
            'success' => false,
            'message' => '',
            'user' => null,
            'requires_password_change' => false
        ];
        
        try {
            // Clean identifier
            $identifier = trim($identifier);
            
            // Check if account is locked
            if ($this->isAccountLocked($identifier)) {
                $this->logLoginAttempt($identifier, null, false, 'Account locked');
                $result['message'] = 'Account is temporarily locked due to multiple failed login attempts. Please try again later.';
                return $result;
            }
            
            // Find user by email or phone number
            $user = $this->findUserByIdentifier($identifier);
            
            if (!$user) {
                $this->logLoginAttempt($identifier, null, false, 'User not found');
                $this->incrementFailedAttempts($identifier);
                $result['message'] = 'Invalid login credentials.';
                return $result;
            }
            
            // Check if user has a password set
            if (empty($user['password'])) {
                $result['message'] = 'No password set for this account. Please contact an administrator.';
                return $result;
            }
            
            // Verify password
            if (!$this->security->verifyPassword($password, $user['password'])) {
                $this->logLoginAttempt($identifier, $user['id'], false, 'Invalid password');
                $this->incrementFailedAttempts($identifier);
                $result['message'] = 'Invalid login credentials.';
                return $result;
            }
            
            // Check if account is active
            if ($user['status'] !== 'active') {
                $this->logLoginAttempt($identifier, $user['id'], false, 'Account inactive');
                $result['message'] = 'Account is not active. Please contact an administrator.';
                return $result;
            }
            
            // Successful authentication
            $this->resetFailedAttempts($identifier);
            $this->updateLastLogin($user['id']);
            $this->logLoginAttempt($identifier, $user['id'], true, 'Successful login');
            
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['full_name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_phone'] = $user['phone_number'];
            $_SESSION['last_activity'] = time();
            
            // Check if password change is required
            $requiresPasswordChange = $user['must_change_password'] || $user['temp_password'];
            
            $result['success'] = true;
            $result['user'] = $user;
            $result['requires_password_change'] = $requiresPasswordChange;
            $result['message'] = 'Login successful.';
            
            // Log user activity
            $this->logUserActivity($user['id'], 'login', 'User logged in successfully');
            
        } catch (Exception $e) {
            error_log("User authentication error: " . $e->getMessage());
            $result['message'] = 'An error occurred during authentication. Please try again.';
        }
        
        return $result;
    }
    
    /**
     * Find user by email or phone number
     * 
     * @param string $identifier Email or phone number
     * @return array|null User data or null if not found
     */
    private function findUserByIdentifier($identifier) {
        // Try email first
        $stmt = $this->pdo->prepare("
            SELECT id, full_name, first_name, last_name, email, phone_number, password, 
                   status, must_change_password, temp_password, email_verified, phone_verified,
                   last_login_at, created_at
            FROM members 
            WHERE email = ? AND status = 'active'
        ");
        $stmt->execute([$identifier]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            return $user;
        }
        
        // Try phone number
        $stmt = $this->pdo->prepare("
            SELECT id, full_name, first_name, last_name, email, phone_number, password, 
                   status, must_change_password, temp_password, email_verified, phone_verified,
                   last_login_at, created_at
            FROM members 
            WHERE phone_number = ? AND status = 'active'
        ");
        $stmt->execute([$identifier]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Check if account is locked due to failed attempts
     * 
     * @param string $identifier Email or phone number
     * @return bool True if account is locked
     */
    private function isAccountLocked($identifier) {
        $stmt = $this->pdo->prepare("
            SELECT login_attempts, locked_until 
            FROM members 
            WHERE (email = ? OR phone_number = ?) AND status = 'active'
        ");
        $stmt->execute([$identifier, $identifier]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            return false;
        }
        
        // Check if locked_until is set and still valid
        if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
            return true;
        }
        
        // Check if max attempts exceeded
        return $user['login_attempts'] >= $this->maxLoginAttempts;
    }
    
    /**
     * Increment failed login attempts
     * 
     * @param string $identifier Email or phone number
     */
    private function incrementFailedAttempts($identifier) {
        $lockUntil = null;
        
        // Get current attempts
        $stmt = $this->pdo->prepare("
            SELECT id, login_attempts 
            FROM members 
            WHERE (email = ? OR phone_number = ?) AND status = 'active'
        ");
        $stmt->execute([$identifier, $identifier]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            $newAttempts = $user['login_attempts'] + 1;
            
            // Lock account if max attempts reached
            if ($newAttempts >= $this->maxLoginAttempts) {
                $lockUntil = date('Y-m-d H:i:s', time() + $this->lockoutDuration);
            }
            
            $stmt = $this->pdo->prepare("
                UPDATE members 
                SET login_attempts = ?, locked_until = ? 
                WHERE id = ?
            ");
            $stmt->execute([$newAttempts, $lockUntil, $user['id']]);
        }
    }
    
    /**
     * Reset failed login attempts
     * 
     * @param string $identifier Email or phone number
     */
    private function resetFailedAttempts($identifier) {
        $stmt = $this->pdo->prepare("
            UPDATE members 
            SET login_attempts = 0, locked_until = NULL 
            WHERE (email = ? OR phone_number = ?) AND status = 'active'
        ");
        $stmt->execute([$identifier, $identifier]);
    }
    
    /**
     * Update last login information
     * 
     * @param int $userId User ID
     */
    private function updateLastLogin($userId) {
        $stmt = $this->pdo->prepare("
            UPDATE members 
            SET last_login_at = NOW(), last_login_ip = ? 
            WHERE id = ?
        ");
        $stmt->execute([$_SERVER['REMOTE_ADDR'] ?? 'unknown', $userId]);
    }
    
    /**
     * Log login attempt
     * 
     * @param string $identifier Email or phone number used
     * @param int|null $memberId Member ID if found
     * @param bool $success Whether login was successful
     * @param string $reason Reason for failure or success
     */
    private function logLoginAttempt($identifier, $memberId, $success, $reason) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO user_login_attempts 
                (identifier, member_id, ip_address, user_agent, success, failure_reason) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $identifier,
                $memberId,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                $success ? 1 : 0,
                $success ? null : $reason
            ]);
        } catch (Exception $e) {
            error_log("Failed to log login attempt: " . $e->getMessage());
        }
    }
    
    /**
     * Log user activity
     * 
     * @param int $memberId Member ID
     * @param string $activityType Type of activity
     * @param string $description Activity description
     * @param string|null $entityType Entity type affected
     * @param int|null $entityId Entity ID affected
     * @param array|null $metadata Additional metadata
     */
    public function logUserActivity($memberId, $activityType, $description, $entityType = null, $entityId = null, $metadata = null) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO user_activity_logs 
                (member_id, activity_type, activity_description, entity_type, entity_id, ip_address, user_agent, metadata) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $memberId,
                $activityType,
                $description,
                $entityType,
                $entityId,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                $metadata ? json_encode($metadata) : null
            ]);
        } catch (Exception $e) {
            error_log("Failed to log user activity: " . $e->getMessage());
        }
    }
    
    /**
     * Check if user is authenticated
     * 
     * @return bool True if user is authenticated
     */
    public function isAuthenticated() {
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $this->sessionTimeout)) {
            $this->logout();
            return false;
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * Get current authenticated user
     * 
     * @return array|null User data or null if not authenticated
     */
    public function getCurrentUser() {
        if (!$this->isAuthenticated()) {
            return null;
        }
        
        $stmt = $this->pdo->prepare("
            SELECT id, full_name, first_name, last_name, email, phone_number, 
                   image_path, birth_date, home_address, occupation, status,
                   email_verified, phone_verified, profile_visibility,
                   notification_preferences, timezone, language, last_login_at
            FROM members 
            WHERE id = ? AND status = 'active'
        ");
        $stmt->execute([$_SESSION['user_id']]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Logout user
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            $this->logUserActivity($_SESSION['user_id'], 'logout', 'User logged out');
        }
        
        // Clear user session variables
        unset($_SESSION['user_id']);
        unset($_SESSION['user_name']);
        unset($_SESSION['user_email']);
        unset($_SESSION['user_phone']);
        unset($_SESSION['last_activity']);
        
        // Destroy session if no admin session
        if (!isset($_SESSION['admin_id'])) {
            session_destroy();
        }
    }
    
    /**
     * Generate password reset token
     * 
     * @param string $identifier Email or phone number
     * @return array Result with success status and message
     */
    public function generatePasswordResetToken($identifier) {
        $result = ['success' => false, 'message' => ''];
        
        try {
            $user = $this->findUserByIdentifier($identifier);
            
            if (!$user) {
                $result['message'] = 'No account found with that email or phone number.';
                return $result;
            }
            
            // Generate token
            $token = $this->security->generateToken(32);
            $expires = date('Y-m-d H:i:s', strtotime('+4 hours'));
            
            // Save token
            $stmt = $this->pdo->prepare("
                UPDATE members 
                SET password_reset_token = ?, password_reset_expires = ? 
                WHERE id = ?
            ");
            $stmt->execute([$token, $expires, $user['id']]);
            
            $result['success'] = true;
            $result['token'] = $token;
            $result['user'] = $user;
            $result['message'] = 'Password reset token generated successfully.';
            
        } catch (Exception $e) {
            error_log("Password reset token generation error: " . $e->getMessage());
            $result['message'] = 'An error occurred. Please try again.';
        }
        
        return $result;
    }
    
    /**
     * Validate password reset token
     * 
     * @param string $token Reset token
     * @return array|null User data if token is valid, null otherwise
     */
    public function validatePasswordResetToken($token) {
        $stmt = $this->pdo->prepare("
            SELECT id, full_name, email, password_reset_expires 
            FROM members 
            WHERE password_reset_token = ? AND status = 'active'
        ");
        $stmt->execute([$token]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            return null;
        }
        
        // Check if token has expired
        if (strtotime($user['password_reset_expires']) < time()) {
            return null;
        }
        
        return $user;
    }
    
    /**
     * Reset user password
     * 
     * @param string $token Reset token
     * @param string $newPassword New password
     * @return array Result with success status and message
     */
    public function resetPassword($token, $newPassword) {
        $result = ['success' => false, 'message' => ''];
        
        try {
            $user = $this->validatePasswordResetToken($token);
            
            if (!$user) {
                $result['message'] = 'Invalid or expired reset token.';
                return $result;
            }
            
            // Validate password strength
            if (!$this->security->validateInput($newPassword, 'password')) {
                $result['message'] = 'Password does not meet security requirements.';
                return $result;
            }
            
            // Hash password
            $hashedPassword = $this->security->hashPassword($newPassword);
            
            // Update password and clear reset token
            $stmt = $this->pdo->prepare("
                UPDATE members 
                SET password = ?, password_reset_token = NULL, password_reset_expires = NULL,
                    password_changed_at = NOW(), must_change_password = 0, temp_password = 0
                WHERE id = ?
            ");
            $stmt->execute([$hashedPassword, $user['id']]);
            
            $result['success'] = true;
            $result['message'] = 'Password reset successfully.';
            
            // Log activity
            $this->logUserActivity($user['id'], 'password_reset', 'Password reset via token');
            
        } catch (Exception $e) {
            error_log("Password reset error: " . $e->getMessage());
            $result['message'] = 'An error occurred. Please try again.';
        }
        
        return $result;
    }

    /**
     * Change user password
     *
     * @param int $userId User ID
     * @param string $currentPassword Current password
     * @param string $newPassword New password
     * @return array Result with success status and message
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        $result = ['success' => false, 'message' => ''];

        try {
            // Get user data
            $stmt = $this->pdo->prepare("SELECT password FROM members WHERE id = ? AND status = 'active'");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                $result['message'] = 'User not found.';
                return $result;
            }

            // Verify current password (skip if user has temp password)
            $stmt = $this->pdo->prepare("SELECT temp_password FROM members WHERE id = ?");
            $stmt->execute([$userId]);
            $tempPasswordFlag = $stmt->fetchColumn();

            if (!$tempPasswordFlag && !$this->security->verifyPassword($currentPassword, $user['password'])) {
                $result['message'] = 'Current password is incorrect.';
                return $result;
            }

            // Validate new password
            if (!$this->security->validateInput($newPassword, 'password')) {
                $result['message'] = 'New password does not meet security requirements.';
                return $result;
            }

            // Hash new password
            $hashedPassword = $this->security->hashPassword($newPassword);

            // Update password
            $stmt = $this->pdo->prepare("
                UPDATE members
                SET password = ?, password_changed_at = NOW(), must_change_password = 0, temp_password = 0
                WHERE id = ?
            ");
            $stmt->execute([$hashedPassword, $userId]);

            $result['success'] = true;
            $result['message'] = 'Password changed successfully.';

            // Log activity
            $this->logUserActivity($userId, 'password_change', 'User changed password');

        } catch (Exception $e) {
            error_log("Password change error: " . $e->getMessage());
            $result['message'] = 'An error occurred. Please try again.';
        }

        return $result;
    }

    /**
     * Create user account (for admin use)
     *
     * @param array $userData User data
     * @param bool $sendEmail Whether to send welcome email with temp password
     * @return array Result with success status and message
     */
    public function createUserAccount($userData, $sendEmail = true) {
        $result = ['success' => false, 'message' => '', 'temp_password' => null];

        try {
            // Validate required fields
            $required = ['full_name', 'email'];
            foreach ($required as $field) {
                if (empty($userData[$field])) {
                    $result['message'] = "Missing required field: $field";
                    return $result;
                }
            }

            // Check if email already exists
            $stmt = $this->pdo->prepare("SELECT id FROM members WHERE email = ?");
            $stmt->execute([$userData['email']]);
            if ($stmt->fetch()) {
                $result['message'] = 'Email address already exists.';
                return $result;
            }

            // Generate temporary password
            $tempPassword = $this->generateTempPassword();
            $hashedPassword = $this->security->hashPassword($tempPassword);

            // Prepare user data
            $insertData = [
                'full_name' => $userData['full_name'],
                'first_name' => $userData['first_name'] ?? '',
                'last_name' => $userData['last_name'] ?? '',
                'email' => $userData['email'],
                'phone_number' => $userData['phone_number'] ?? null,
                'birth_date' => $userData['birth_date'] ?? null,
                'home_address' => $userData['home_address'] ?? null,
                'occupation' => $userData['occupation'] ?? null,
                'password' => $hashedPassword,
                'temp_password' => 1,
                'must_change_password' => 1,
                'status' => 'active'
            ];

            // Insert user
            $stmt = $this->pdo->prepare("
                INSERT INTO members
                (full_name, first_name, last_name, email, phone_number, birth_date,
                 home_address, occupation, password, temp_password, must_change_password, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $insertData['full_name'],
                $insertData['first_name'],
                $insertData['last_name'],
                $insertData['email'],
                $insertData['phone_number'],
                $insertData['birth_date'],
                $insertData['home_address'],
                $insertData['occupation'],
                $insertData['password'],
                $insertData['temp_password'],
                $insertData['must_change_password'],
                $insertData['status']
            ]);

            $userId = $this->pdo->lastInsertId();

            $result['success'] = true;
            $result['user_id'] = $userId;
            $result['temp_password'] = $tempPassword;
            $result['message'] = 'User account created successfully.';

            // Log activity
            $this->logUserActivity($userId, 'account_created', 'User account created by admin');

        } catch (Exception $e) {
            error_log("User account creation error: " . $e->getMessage());
            $result['message'] = 'An error occurred while creating the account.';
        }

        return $result;
    }

    /**
     * Generate temporary password
     *
     * @return string Temporary password
     */
    private function generateTempPassword() {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%';
        $password = '';
        for ($i = 0; $i < 12; $i++) {
            $password .= $chars[random_int(0, strlen($chars) - 1)];
        }
        return $password;
    }

    /**
     * Update user profile
     *
     * @param int $userId User ID
     * @param array $profileData Profile data to update
     * @return array Result with success status and message
     */
    public function updateProfile($userId, $profileData) {
        $result = ['success' => false, 'message' => ''];

        try {
            // Define allowed fields for update
            $allowedFields = [
                'full_name', 'first_name', 'last_name', 'phone_number',
                'birth_date', 'home_address', 'occupation', 'image_path',
                'profile_visibility', 'timezone', 'language'
            ];

            $updateFields = [];
            $updateValues = [];

            foreach ($allowedFields as $field) {
                if (isset($profileData[$field])) {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = $profileData[$field];
                }
            }

            if (empty($updateFields)) {
                $result['message'] = 'No valid fields to update.';
                return $result;
            }

            // Add user ID to values
            $updateValues[] = $userId;

            // Update profile
            $sql = "UPDATE members SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($updateValues);

            $result['success'] = true;
            $result['message'] = 'Profile updated successfully.';

            // Log activity
            $this->logUserActivity($userId, 'profile_update', 'User updated profile', 'profile', $userId, $profileData);

        } catch (Exception $e) {
            error_log("Profile update error: " . $e->getMessage());
            $result['message'] = 'An error occurred while updating profile.';
        }

        return $result;
    }

    /**
     * Get user preferences
     *
     * @param int $userId User ID
     * @return array User preferences
     */
    public function getUserPreferences($userId) {
        $preferences = [];

        try {
            $stmt = $this->pdo->prepare("
                SELECT preference_key, preference_value, preference_type
                FROM user_preferences
                WHERE member_id = ?
            ");
            $stmt->execute([$userId]);

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $value = $row['preference_value'];

                // Convert value based on type
                switch ($row['preference_type']) {
                    case 'boolean':
                        $value = (bool) $value;
                        break;
                    case 'integer':
                        $value = (int) $value;
                        break;
                    case 'json':
                        $value = json_decode($value, true);
                        break;
                }

                $preferences[$row['preference_key']] = $value;
            }

        } catch (Exception $e) {
            error_log("Get user preferences error: " . $e->getMessage());
        }

        return $preferences;
    }

    /**
     * Set user preference
     *
     * @param int $userId User ID
     * @param string $key Preference key
     * @param mixed $value Preference value
     * @param string $type Value type (string, integer, boolean, json)
     * @return bool Success status
     */
    public function setUserPreference($userId, $key, $value, $type = 'string') {
        try {
            // Convert value based on type
            switch ($type) {
                case 'boolean':
                    $value = $value ? '1' : '0';
                    break;
                case 'integer':
                    $value = (string) (int) $value;
                    break;
                case 'json':
                    $value = json_encode($value);
                    break;
                default:
                    $value = (string) $value;
            }

            $stmt = $this->pdo->prepare("
                INSERT INTO user_preferences (member_id, preference_key, preference_value, preference_type)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                preference_value = VALUES(preference_value),
                preference_type = VALUES(preference_type),
                updated_at = NOW()
            ");

            $stmt->execute([$userId, $key, $value, $type]);

            return true;

        } catch (Exception $e) {
            error_log("Set user preference error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user by ID
     *
     * @param int $userId User ID
     * @return array|null User data or null if not found
     */
    public function getUserById($userId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT id, full_name, first_name, last_name, email, phone_number,
                       birth_date, home_address, occupation, image_path, status,
                       temp_password, must_change_password, email_verified,
                       last_login_at, created_at, updated_at
                FROM members
                WHERE id = ? AND status = 'active'
            ");
            $stmt->execute([$userId]);

            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Get user by ID error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if user is authenticated
     *
     * @return bool True if user is authenticated, false otherwise
     */
    public function isAuthenticated() {
        // Check if session variables are set
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_name']) || !isset($_SESSION['user_email'])) {
            return false;
        }

        // Check session timeout (optional - 24 hours)
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 86400)) {
            return false;
        }

        // Update last activity
        $_SESSION['last_activity'] = time();

        return true;
    }
}
