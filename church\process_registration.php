<?php
// Force PHP to process this file
declare(strict_types=1);

// Check PHP execution
if (!defined('PHP_VERSION_ID')) {
    die('PHP is not being processed correctly');
}

// Check PHP version compatibility
if (PHP_VERSION_ID < 80000) {
    die('This application requires PHP 8.0 or higher. Current version: ' . PHP_VERSION);
}

require_once 'config.php';
require_once 'classes/SecurityManager.php';
require_once 'classes/UserAuthManager.php';

// Initialize error logging
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Ensure logs directory exists and is writable
$logDir = __DIR__ . '/logs';
$logFile = $logDir . '/registration_errors.log';

if (!is_dir($logDir)) {
    if (!@mkdir($logDir, 0755, true)) {
        error_log('Failed to create logs directory. Please check server permissions.');
        die('An error occurred. Please contact the administrator.');
    }
}

if (!is_writable($logDir)) {
    error_log('Logs directory is not writable: ' . $logDir);
    die('An error occurred. Please contact the administrator.');
}

ini_set('error_log', $logFile);

// Validate server environment
if (!extension_loaded('pdo')) {
    error_log('PDO extension is not loaded');
    die('Server configuration error. Please contact the administrator.');
}

if (!extension_loaded('pdo_mysql')) {
    error_log('PDO MySQL extension is not loaded');
    die('Server configuration error. Please contact the administrator.');
}

if (!extension_loaded('fileinfo')) {
    error_log('Fileinfo extension is not loaded');
    die('Server configuration error. Please contact the administrator.');
}

// Function to log errors
function logError($message, $context = []) {
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' Context: ' . json_encode($context, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) : '';
    $serverInfo = [
        'PHP_VERSION' => PHP_VERSION,
        'SERVER_SOFTWARE' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? '',
        'HTTP_REFERER' => $_SERVER['HTTP_REFERER'] ?? '',
        'REMOTE_ADDR' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
    ];
    $logMessage = sprintf("[%s] %s%s Server Info: %s\n",
        $timestamp,
        $message,
        $contextStr,
        json_encode($serverInfo, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)
    );
    error_log($logMessage, 3, $GLOBALS['logFile']);
}

// Function to validate file upload
function validateFileUpload($file) {
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    $max_size = 5 * 1024 * 1024; // 5MB

    if ($file['error'] !== UPLOAD_ERR_OK) {
        $upload_errors = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize directive',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];
        $error_message = isset($upload_errors[$file['error']]) ? 
                        $upload_errors[$file['error']] : 
                        'Unknown upload error';
        throw new Exception("File upload error: $error_message");
    }

    if (!in_array($file['type'], $allowed_types)) {
        throw new Exception('Invalid file type. Only JPEG, PNG, and GIF files are allowed.');
    }

    if ($file['size'] > $max_size) {
        throw new Exception('File size exceeds limit of 5MB.');
    }

    return true;
}

// Verify POST data is being received
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log('POST data received: ' . print_r($_POST, true));
} else {
    error_log('No POST data received');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Initialize security manager
        $security = new SecurityManager($pdo);
        $userAuth = new UserAuthManager($pdo, $security);

        // Validate required fields
        $required_fields = ['full_name', 'email', 'birth_date', 'password', 'confirm_password'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Required field '$field' is missing.");
            }
        }

        // Validate email format
        if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email format.');
        }

        // Validate password match
        if ($_POST['password'] !== $_POST['confirm_password']) {
            throw new Exception('Passwords do not match.');
        }

        // Validate password strength
        if (!$security->validateInput($_POST['password'], 'password')) {
            throw new Exception('Password does not meet security requirements. Please use a stronger password.');
        }

        // Sanitize input data
        $full_name = $security->sanitizeInput($_POST['full_name'], 'text');
        $occupation = $security->sanitizeInput($_POST['occupation'] ?? '', 'text');
        $email = $security->sanitizeInput($_POST['email'], 'email');
        $phone_number = $security->sanitizeInput($_POST['phone_number'] ?? '', 'text');
        $home_address = $security->sanitizeInput($_POST['home_address'] ?? '', 'text');
        $birth_date = $security->sanitizeInput($_POST['birth_date'], 'text');
        $message = $security->sanitizeInput($_POST['message'] ?? '', 'text');
        $password = $_POST['password']; // Don't sanitize password, just hash it
        
        // Split full name into first and last name
        $name_parts = explode(' ', $full_name, 2);
        $first_name = $name_parts[0];
        $last_name = isset($name_parts[1]) ? $name_parts[1] : '';

        // Hash password
        $hashedPassword = $security->hashPassword($password);
        
        // Handle file upload
        $image_path = null;
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] !== UPLOAD_ERR_NO_FILE) {
            try {
                validateFileUpload($_FILES['profile_image']);
                
                $upload_dir = __DIR__ . '/uploads/';
                if (!is_dir($upload_dir)) {
                    if (!mkdir($upload_dir, 0755, true)) {
                        throw new Exception('Failed to create upload directory.');
                    }
                }
                
                if (!is_writable($upload_dir)) {
                    error_log('Upload directory is not writable: ' . $upload_dir);
                    throw new Exception('Server configuration error. Please contact the administrator.');
                }
                
                $file_extension = strtolower(pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION));
                $file_name = uniqid() . '.' . $file_extension;
                $target_path = $upload_dir . $file_name;
                
                if (!move_uploaded_file($_FILES['profile_image']['tmp_name'], $target_path)) {
                    throw new Exception('Failed to move uploaded file.');
                }
                
                $image_path = 'uploads/' . $file_name;
            } catch (Exception $e) {
                logError('File upload error: ' . $e->getMessage(), [
                    'file' => $_FILES['profile_image']['name'],
                    'type' => $_FILES['profile_image']['type'],
                    'size' => $_FILES['profile_image']['size']
                ]);
                header('Location: index.php?error=file_upload&message=' . urlencode($e->getMessage()));
                exit;
            }
        }
        
        // Database operation
        try {
            $stmt = $pdo->prepare("INSERT INTO members (full_name, occupation, image_path, email, phone_number, home_address, birth_date, message, first_name, last_name, password, temp_password, must_change_password, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

            $stmt->execute([
                $full_name,
                $occupation,
                $image_path,
                $email,
                $phone_number,
                $home_address,
                $birth_date,
                $message,
                $first_name,
                $last_name,
                $hashedPassword,
                0, // temp_password = false (user created their own password)
                0, // must_change_password = false
                'active', // status
                0  // email_verified = false (can be verified later)
            ]);

            $userId = $pdo->lastInsertId();

            // Log user registration activity
            $userAuth->logUserActivity($userId, 'registration', 'User registered new account with password');

            // Send welcome email
            $memberData = [
                'full_name' => $full_name,
                'first_name' => $first_name,
                'last_name' => $last_name,
                'email' => $email,
                'phone_number' => $phone_number,
                'home_address' => $home_address,
                'occupation' => $occupation,
                'image_path' => $image_path
            ];

            try {
                $emailSent = sendWelcomeEmail($memberData);
                if (!$emailSent) {
                    logError('Failed to send welcome email', ['email' => $email]);
                }
                
                header('Location: index.php?success=1&email=' . ($emailSent ? '1' : '0'));
                exit;
            } catch (Exception $e) {
                logError('Email sending error: ' . $e->getMessage(), ['email' => $email]);
                header('Location: index.php?success=1&email=0');
                exit;
            }
        } catch (PDOException $e) {
            logError('Database error: ' . $e->getMessage(), [
                'code' => $e->getCode(),
                'email' => $email
            ]);
            
            if ($e->getCode() == 23000) { // Duplicate entry error
                header('Location: index.php?error=duplicate_email');
            } else {
                header('Location: index.php?error=db_error');
            }
            exit;
        }
    } catch (Exception $e) {
        logError('General error: ' . $e->getMessage(), $_POST);
        header('Location: index.php?error=general&message=' . urlencode($e->getMessage()));
        exit;
    }
} else {
    header('Location: index.php');
    exit;
}